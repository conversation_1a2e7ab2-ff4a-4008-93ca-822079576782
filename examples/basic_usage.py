#!/usr/bin/env python3
"""
URL JSON MCP 2.0 基本使用示例
"""

import asyncio
import json
import sys
import os

# 添加父目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import AsyncURLContentManager


async def example_json_fetching():
    """JSON数据获取示例"""
    print("=== JSON数据获取示例 ===")
    
    async with AsyncURLContentManager(cache_duration_minutes=10) as manager:
        # 获取GitHub API数据
        github_url = "https://api.github.com/repos/microsoft/vscode"
        print(f"获取GitHub仓库信息: {github_url}")
        
        result = await manager.fetch_json(github_url)
        
        if result['success']:
            data = result['data']
            print(f"仓库名称: {data.get('name', 'N/A')}")
            print(f"描述: {data.get('description', 'N/A')}")
            print(f"星标数: {data.get('stargazers_count', 'N/A')}")
            print(f"语言: {data.get('language', 'N/A')}")
            print(f"数据来源: {result['source']}")
        else:
            print(f"获取失败: {result['error']}")


async def example_coding_standards():
    """编码规范获取示例"""
    print(f"\n=== 编码规范获取示例 ===")
    
    async with AsyncURLContentManager(cache_duration_minutes=30) as manager:
        # 获取示例文本内容
        standards_url = "https://httpbin.org/get"
        domain = "Python, FastMCP, Async Programming"
        
        print(f"获取编码规范: {standards_url}")
        print(f"领域: {domain}")
        
        result = await manager.fetch_coding_standards(standards_url, domain)
        
        if result['success']:
            content = result['content']
            print(f"内容长度: {len(content)} 字符")
            print(f"领域: {result['domain']}")
            print(f"数据来源: {result['source']}")
            print(f"内容预览: {content[:200]}...")
        else:
            print(f"获取失败: {result['error']}")


async def example_cache_management():
    """缓存管理示例"""
    print(f"\n=== 缓存管理示例 ===")
    
    async with AsyncURLContentManager(cache_duration_minutes=5) as manager:
        # 先获取一些数据以填充缓存
        urls = [
            "https://httpbin.org/json",
            "https://httpbin.org/get"
        ]
        
        print("填充缓存...")
        for url in urls:
            await manager.fetch_json(url)
        
        # 查看缓存信息
        cache_info = manager.get_cache_info()
        print(f"缓存条目数: {cache_info['total_entries']}")
        print(f"缓存持续时间: {cache_info['cache_duration_minutes']} 分钟")
        
        for entry in cache_info['entries']:
            print(f"  - URL: {entry['url'][:50]}...")
            print(f"    缓存时间: {entry['cached_time']}")
            print(f"    年龄: {entry['age_minutes']:.2f} 分钟")
            print(f"    有效: {entry['is_valid']}")
        
        # 清理缓存
        print(f"\n清理缓存...")
        cleared_count = manager.clear_cache()
        print(f"清理了 {cleared_count} 个缓存条目")


async def example_error_handling():
    """错误处理示例"""
    print(f"\n=== 错误处理示例 ===")
    
    async with AsyncURLContentManager() as manager:
        # 测试各种错误情况
        error_cases = [
            ("无效域名", "https://invalid-domain-12345.com/api"),
            ("404错误", "https://httpbin.org/status/404"),
            ("超时测试", "https://httpbin.org/delay/35")  # 超过默认30秒超时
        ]
        
        for case_name, url in error_cases:
            print(f"\n--- {case_name} ---")
            print(f"URL: {url}")
            
            result = await manager.fetch_json(url)
            
            if result['success']:
                print("意外成功!")
            else:
                print(f"错误类型: {result.get('error_type', 'unknown')}")
                print(f"错误信息: {result.get('error', 'No error message')}")
                print(f"HTTP方法: {result.get('method', 'N/A')}")


async def example_configuration():
    """配置示例"""
    print(f"\n=== 配置示例 ===")
    
    # 使用自定义配置
    custom_manager = AsyncURLContentManager(cache_duration_minutes=1)
    
    async with custom_manager as manager:
        print(f"自定义缓存时间: 1分钟")
        
        # 获取数据
        result = await manager.fetch_json("https://httpbin.org/json")
        print(f"首次获取: {result.get('source', 'unknown')}")
        
        # 立即再次获取（应该来自缓存）
        result = await manager.fetch_json("https://httpbin.org/json")
        print(f"二次获取: {result.get('source', 'unknown')}")
        
        # 等待缓存过期
        print("等待缓存过期...")
        await asyncio.sleep(65)  # 等待超过1分钟
        
        result = await manager.fetch_json("https://httpbin.org/json")
        print(f"过期后获取: {result.get('source', 'unknown')}")


async def main():
    """主示例函数"""
    print("URL JSON MCP 2.0 使用示例")
    print("=" * 50)
    
    try:
        await example_json_fetching()
        await example_coding_standards()
        await example_cache_management()
        await example_error_handling()
        await example_configuration()
        
        print(f"\n" + "=" * 50)
        print("所有示例执行完成!")
        
    except KeyboardInterrupt:
        print(f"\n示例被用户中断")
    except Exception as e:
        print(f"\n示例执行异常: {str(e)}")


if __name__ == "__main__":
    # 设置示例环境
    os.environ['FASTMCP_LOG_LEVEL'] = 'INFO'
    
    asyncio.run(main())
