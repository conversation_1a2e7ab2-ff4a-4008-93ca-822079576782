import sys
import json
from typing import Any, Dict, Optional, <PERSON><PERSON>
from datetime import datetime, timedelta
import httpx
from fastmcp import <PERSON><PERSON><PERSON>

# Redirect all debug prints to stderr
def debug_print(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)


class URLContentManager:
    """管理URL内容获取和缓存的类"""

    def __init__(self, cache_duration_minutes: int = 30):
        """
        初始化URL内容管理器

        Args:
            cache_duration_minutes: 缓存持续时间（分钟）
        """
        self.cache: Dict[str, Tuple[datetime, Any]] = {}
        self.cache_duration = timedelta(minutes=cache_duration_minutes)
        self.client = httpx.Client(
            timeout=30.0,
            headers={
                'User-Agent': 'URL-JSON-MCP/1.0 (httpx)'
            }
        )
        debug_print(f"URLContentManager initialized with {cache_duration_minutes}min cache duration")

    def _is_cache_valid(self, url: str) -> bool:
        """检查缓存是否有效"""
        if url not in self.cache:
            return False

        cached_time, _ = self.cache[url]
        return datetime.now() - cached_time < self.cache_duration

    def _get_from_cache(self, url: str) -> Optional[Any]:
        """从缓存获取内容"""
        if self._is_cache_valid(url):
            _, content = self.cache[url]
            debug_print(f"Cache hit for URL: {url}")
            return content
        return None

    def _store_in_cache(self, url: str, content: Any) -> None:
        """存储内容到缓存"""
        self.cache[url] = (datetime.now(), content)
        debug_print(f"Cached content for URL: {url}")

    def _try_http_request(self, url: str, method: str) -> Dict[str, Any]:
        """
        尝试使用指定HTTP方法获取URL内容

        Args:
            url: 要获取的URL
            method: HTTP方法 ('GET' 或 'POST')

        Returns:
            包含请求结果的字典
        """
        return self._execute_http_request(url, method, response_type='json')

    def _execute_http_request(self, url: str, method: str, response_type: str = 'json') -> Dict[str, Any]:
        """
        统一的HTTP请求执行方法

        Args:
            url: 要获取的URL
            method: HTTP方法 ('GET' 或 'POST')
            response_type: 响应类型 ('json' 或 'text')

        Returns:
            包含请求结果的字典
        """
        try:
            debug_print(f"Trying {method} request for {response_type} content to: {url}")

            if method.upper() == 'GET':
                response = self.client.get(url)
            elif method.upper() == 'POST':
                response = self.client.post(url)  # 无请求体的POST
            else:
                return {
                    "success": False,
                    "error": f"Unsupported HTTP method: {method}",
                    "url": url
                }

            response.raise_for_status()  # 抛出HTTP错误

            # 根据响应类型处理内容
            try:
                if response_type == 'json':
                    content = response.json()
                    debug_print(f"Successfully parsed JSON from {url} using {method}")
                    return {
                        "success": True,
                        "data": content,
                        "method": method.upper(),
                        "status_code": response.status_code
                    }
                elif response_type == 'text':
                    content = response.text
                    debug_print(f"Successfully retrieved text content from {url} using {method}")
                    return {
                        "success": True,
                        "content": content,
                        "method": method.upper(),
                        "status_code": response.status_code
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Unsupported response type: {response_type}",
                        "method": method.upper(),
                        "url": url,
                        "status_code": response.status_code
                    }

            except json.JSONDecodeError as e:
                debug_print(f"JSON decode error for {url} using {method}: {e}")
                return {
                    "success": False,
                    "error": f"Invalid JSON response: {str(e)}",
                    "method": method.upper(),
                    "url": url,
                    "status_code": response.status_code
                }
            except Exception as e:
                debug_print(f"Content processing error for {url} using {method}: {e}")
                return {
                    "success": False,
                    "error": f"Failed to process {response_type} content: {str(e)}",
                    "method": method.upper(),
                    "url": url,
                    "status_code": response.status_code
                }

        except httpx.HTTPStatusError as e:
            debug_print(f"HTTP error for {url} using {method}: {e}")
            return {
                "success": False,
                "error": f"HTTP {e.response.status_code}: {e.response.reason_phrase}",
                "method": method.upper(),
                "url": url,
                "status_code": e.response.status_code
            }

        except httpx.RequestError as e:
            debug_print(f"Request error for {url} using {method}: {e}")
            return {
                "success": False,
                "error": f"Network error: {str(e)}",
                "method": method.upper(),
                "url": url
            }

        except Exception as e:
            debug_print(f"Unexpected error for {url} using {method}: {e}")
            return {
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "method": method.upper(),
                "url": url
            }

    def _try_http_request_text(self, url: str, method: str) -> Dict[str, Any]:
        """
        尝试使用指定HTTP方法获取URL文本内容

        Args:
            url: 要获取的URL
            method: HTTP方法 ('GET' 或 'POST')

        Returns:
            包含请求结果的字典
        """
        return self._execute_http_request(url, method, response_type='text')

    def fetch_json(self, url: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        从URL获取JSON内容，先尝试GET请求，失败后尝试POST请求

        Args:
            url: 要获取的URL
            force_refresh: 是否强制刷新缓存

        Returns:
            包含状态和数据的字典
        """
        debug_print(f"Fetching JSON from URL: {url}, force_refresh: {force_refresh}")

        # 检查缓存（除非强制刷新）
        if not force_refresh:
            cached_content = self._get_from_cache(url)
            if cached_content is not None:
                return {
                    "success": True,
                    "data": cached_content,
                    "source": "cache",
                    "url": url
                }

        # 尝试HTTP请求：先GET，后POST
        methods_to_try = ['GET', 'POST']
        last_error = None

        for method in methods_to_try:
            result = self._try_http_request(url, method)

            if result["success"]:
                # 请求成功，存储到缓存并返回
                self._store_in_cache(url, result["data"])

                return {
                    "success": True,
                    "data": result["data"],
                    "source": "network",
                    "url": url,
                    "method": result["method"],
                    "status_code": result["status_code"]
                }
            else:
                # 请求失败，记录错误并尝试下一个方法
                last_error = result
                debug_print(f"{method} request failed for {url}: {result.get('error', 'Unknown error')}")

        # 所有方法都失败，返回最后一个错误
        debug_print(f"All HTTP methods failed for {url}")
        return last_error

    def fetch_coding_standards(self, url: str, domain: str, force_refresh: bool = False) -> Dict[str, Any]:
        """
        从URL获取编码规范文本内容

        Args:
            url: 要获取编码规范的URL
            domain: 编码领域描述（编程语言、框架、技术等）
            force_refresh: 是否强制刷新缓存

        Returns:
            包含状态和编码规范内容的字典
        """
        debug_print(f"Fetching coding standards from URL: {url}, domain: {domain}, force_refresh: {force_refresh}")

        # 使用URL和domain组合作为缓存键，添加前缀避免与JSON缓存冲突
        cache_key = f"coding_standards:{url}:{hash(domain)}"

        # 检查缓存（除非强制刷新）
        if not force_refresh:
            cached_content = self._get_from_cache(cache_key)
            if cached_content is not None:
                return {
                    "success": True,
                    "content": cached_content,
                    "domain": domain,
                    "source": "cache",
                    "url": url
                }

        # 尝试HTTP请求：先GET，后POST
        methods_to_try = ['GET', 'POST']
        last_error = None

        for method in methods_to_try:
            result = self._try_http_request_text(url, method)

            if result["success"]:
                # 请求成功，存储到缓存并返回
                self._store_in_cache(cache_key, result["content"])

                return {
                    "success": True,
                    "content": result["content"],
                    "domain": domain,
                    "source": "network",
                    "url": url,
                    "method": result["method"],
                    "status_code": result["status_code"]
                }
            else:
                # 请求失败，记录错误并尝试下一个方法
                last_error = result
                debug_print(f"{method} request failed for {url}: {result.get('error', 'Unknown error')}")

        # 所有方法都失败，返回最后一个错误
        debug_print(f"All HTTP methods failed for coding standards URL: {url}")
        return {
            "success": False,
            "error": last_error.get("error", "Unknown error"),
            "domain": domain,
            "url": url,
            "method": last_error.get("method", "Unknown"),
            "status_code": last_error.get("status_code")
        }

    def clear_cache(self) -> int:
        """清理所有缓存"""
        cache_size = len(self.cache)
        self.cache.clear()
        debug_print(f"Cleared {cache_size} cached entries")
        return cache_size

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        now = datetime.now()
        cache_info = {
            "total_entries": len(self.cache),
            "cache_duration_minutes": self.cache_duration.total_seconds() / 60,
            "entries": []
        }

        for url, (cached_time, _) in self.cache.items():
            age_minutes = (now - cached_time).total_seconds() / 60
            is_valid = age_minutes < self.cache_duration.total_seconds() / 60

            cache_info["entries"].append({
                "url": url,
                "cached_time": cached_time.isoformat(),
                "age_minutes": round(age_minutes, 2),
                "is_valid": is_valid
            })

        return cache_info

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'client'):
            self.client.close()


# Create MCP server
mcp = FastMCP("url_json_mcp")
url_manager = None


@mcp.tool()
async def fetch_json_from_url(url: str, force_refresh: bool = False) -> str:
    """
    从指定URL获取JSON内容

    Args:
        url: 要获取JSON内容的URL
        force_refresh: 是否强制刷新缓存，忽略现有缓存

    Returns:
        JSON格式的字符串，包含获取的数据或错误信息
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    result = url_manager.fetch_json(url, force_refresh)
    return json.dumps(result, ensure_ascii=False, indent=2)


@mcp.tool()
async def clear_cache() -> str:
    """
    清理所有缓存的URL内容

    Returns:
        包含清理结果的JSON字符串
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    cleared_count = url_manager.clear_cache()
    return json.dumps({
        "success": True,
        "cleared_entries": cleared_count,
        "message": f"Cleared {cleared_count} cached entries"
    }, ensure_ascii=False, indent=2)


@mcp.tool()
async def get_cache_info() -> str:
    """
    获取当前缓存状态信息

    Returns:
        包含缓存信息的JSON字符串
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    cache_info = url_manager.get_cache_info()
    return json.dumps({
        "success": True,
        "cache_info": cache_info
    }, ensure_ascii=False, indent=2)


@mcp.tool()
async def fetch_coding_standards(url: str, domain: str, force_refresh: bool = False) -> str:
    """
    从指定URL获取编码规范内容

    Args:
        url: 编码规范的HTTP端点地址
        domain: 编码领域描述，包含编程语言、框架、技术等信息
        force_refresh: 是否强制刷新缓存，忽略现有缓存

    Returns:
        JSON格式的字符串，包含编码规范内容或错误信息
    """
    if url_manager is None:
        return json.dumps({
            "success": False,
            "error": "URL manager not initialized"
        }, ensure_ascii=False, indent=2)

    result = url_manager.fetch_coding_standards(url, domain, force_refresh)
    return json.dumps(result, ensure_ascii=False, indent=2)


def main():
    """主函数，启动URL JSON MCP服务器"""
    debug_print("Starting URL JSON MCP Server...")

    # Initialize URL manager
    global url_manager
    url_manager = URLContentManager(cache_duration_minutes=30)
    debug_print("URL manager initialized")

    # Run MCP server
    mcp.run(transport='stdio')


if __name__ == "__main__":
    main()