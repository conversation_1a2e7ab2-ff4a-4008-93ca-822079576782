
# URL JSON MCP 2.1

这是一个基于FastMCP 2.0的现代化URL内容获取MCP服务器，专为编码助手（如Cursor、Augment等）设计。

## 🚀 功能特点

- **异步架构**：基于FastMCP 2.0，采用async/await模式，性能更优
- **URL JSON获取**：从任何URL获取JSON格式的数据
- **编码规范获取**：从指定URL获取编码规范文本内容
- **智能缓存**：自动缓存获取的内容，支持过期清理，避免重复请求
- **标签系统**：工具按功能分类，支持标签过滤
- **环境变量配置**：支持FASTMCP_*环境变量配置
- **结构化错误处理**：完善的错误分类和恢复机制
- **专业日志系统**：集成logging模块，支持多级别日志
- **连接池管理**：优化的HTTP连接池和资源管理
- **自定义序列化**：优化的JSON输出格式

## 主要用途

- 获取API数据用于代码开发
- 获取编码规范和开发标准
- 获取配置文件或元数据
- 集成到编码助手工作流中
- 减少重复的网络请求


# 工具

此MCP服务器提供以下工具：

### JSON获取工具

* `fetch_json_from_url` - 从指定URL获取JSON内容
  - `url`: 要获取的URL地址
  - `force_refresh`: 是否强制刷新缓存（可选，默认false）

### 编码规范获取工具

* `fetch_coding_standards` - 从指定URL获取编码规范内容
  - `url`: 编码规范的HTTP端点地址
  - `domain`: 编码领域描述（编程语言、框架、技术等）
  - `force_refresh`: 是否强制刷新缓存（可选，默认false）

### 缓存管理工具

* `clear_cache` - 清理所有缓存的URL内容
* `get_cache_info` - 获取当前缓存状态信息，包括：
  - 缓存条目数量
  - 缓存持续时间设置
  - 每个缓存条目的详细信息（URL、缓存时间、有效性等）


# 安装和配置

## 前提条件

1. 支持MCP的编码助手（如Cursor、Augment等）
2. 确保已安装 `uv`，可以参考 [这些说明](https://modelcontextprotocol.io/quickstart/server)
3. Python 3.10+ 和 FastMCP 2.0+

## 安装步骤

1. 将此项目文件放在你的工作目录中

2. 配置编码助手的MCP设置：
   - **Cursor/Augment**: 通常会自动检测项目中的MCP配置
   - 如需手动配置，添加以下配置：

```json
{
  "mcpServers": {
    "url_json_mcp": {
      "command": "uv",
      "args": ["run", "main.py"]
    }
  }
}
```

或者使用Python直接运行：

```json
{
  "mcpServers": {
    "url_json_mcp": {
      "command": "python",
      "args": ["main.py"]
    }
  }
}
```

3. 重启你的编码助手

> **注意**: Cursor和Augment等现代编码助手通常会自动检测和加载项目中的MCP服务器。

## 使用示例

```
# 获取JSON数据
fetch_json_from_url("https://api.github.com/repos/microsoft/vscode")

# 获取编码规范
fetch_coding_standards("https://example.com/python-standards.txt", "Python, Django, REST API")

# 强制刷新缓存
fetch_json_from_url("https://api.example.com/data", force_refresh=true)
fetch_coding_standards("https://example.com/standards.txt", "JavaScript, React", force_refresh=true)

# 查看缓存状态
get_cache_info()

# 清理缓存
clear_cache()
```

## 🔧 环境变量配置

URL JSON MCP 2.0 支持通过环境变量进行配置：

| 环境变量 | 描述 | 默认值 |
|---------|------|--------|
| `FASTMCP_CACHE_DURATION` | 缓存持续时间（分钟） | 30 |
| `FASTMCP_HTTP_TIMEOUT` | HTTP请求超时时间（秒） | 30.0 |
| `FASTMCP_USER_AGENT` | HTTP请求User-Agent | URL-JSON-MCP/2.0 (httpx) |
| `FASTMCP_LOG_LEVEL` | 日志级别 | INFO |
| `FASTMCP_MASK_ERROR_DETAILS` | 是否隐藏错误详情 | false |
| `FASTMCP_TRANSPORT` | 传输协议 | stdio |
| `FASTMCP_HOST` | HTTP服务器主机 | 127.0.0.1 |
| `FASTMCP_PORT` | HTTP服务器端口 | 8080 |

### 配置示例

```bash
# 设置缓存时间为60分钟
export FASTMCP_CACHE_DURATION=60

# 启用调试日志
export FASTMCP_LOG_LEVEL=DEBUG

# 使用HTTP传输
export FASTMCP_TRANSPORT=http
export FASTMCP_HOST=0.0.0.0
export FASTMCP_PORT=9000
```

## 🏷️ 工具标签

所有工具都使用标签进行分类：

- `public`: 公开可用的工具
- `api`: API相关功能
- `json`: JSON数据处理
- `text`: 文本内容处理
- `cache`: 缓存管理
- `info`: 信息查询
- `management`: 管理功能
- `standards`: 编码规范相关
