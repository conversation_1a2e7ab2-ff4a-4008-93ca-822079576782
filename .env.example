# URL JSON MCP 2.0 环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# 缓存配置
FASTMCP_CACHE_DURATION=30  # 缓存持续时间（分钟）

# HTTP配置
FASTMCP_HTTP_TIMEOUT=30.0  # HTTP请求超时时间（秒）
FASTMCP_USER_AGENT=URL-JSON-MCP/2.0 (httpx)  # HTTP请求User-Agent

# 日志配置
FASTMCP_LOG_LEVEL=INFO  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
FASTMCP_MASK_ERROR_DETAILS=false  # 是否隐藏错误详情

# 传输配置
FASTMCP_TRANSPORT=stdio  # 传输协议：stdio, http
FASTMCP_HOST=127.0.0.1  # HTTP服务器主机（仅在transport=http时使用）
FASTMCP_PORT=8080  # HTTP服务器端口（仅在transport=http时使用）

# 开发环境示例配置
# FASTMCP_LOG_LEVEL=DEBUG
# FASTMCP_CACHE_DURATION=5
# FASTMCP_HTTP_TIMEOUT=10.0

# 生产环境示例配置
# FASTMCP_LOG_LEVEL=WARNING
# FASTMCP_MASK_ERROR_DETAILS=true
# FASTMCP_CACHE_DURATION=60
# FASTMCP_TRANSPORT=http
# FASTMCP_HOST=0.0.0.0
# FASTMCP_PORT=9000
