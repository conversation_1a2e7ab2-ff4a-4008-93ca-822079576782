[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "url-json-mcp"
version = "2.1.0"
description = "FastMCP 2.0 server for fetching JSON content and coding standards from URLs with async caching support"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "URL JSON MCP", email = "<EMAIL>"},
]
keywords = ["mcp", "json", "url", "api", "cache", "fastmcp", "async", "coding-standards"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Code Generators",
    "Framework :: AsyncIO",
]
dependencies = [
    "httpx>=0.28.1",
    "fastmcp>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-httpx>=0.21.0",
]

[project.urls]
Homepage = "https://github.com/example/url-json-mcp"
Repository = "https://github.com/example/url-json-mcp"
Issues = "https://github.com/example/url-json-mcp/issues"

[project.scripts]
url-json-mcp = "main:main"

[tool.hatch.build.targets.wheel]
include = [
    "main.py",
    "README.md",
]

[tool.hatch.build.targets.sdist]
include = [
    "main.py",
    "README.md",
    "pyproject.toml",
]
