#!/usr/bin/env python3
"""
URL JSON MCP 2.0 异步管理器测试
"""

import asyncio
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import AsyncURLContentManager, config, logger


async def test_async_manager():
    """测试异步URL内容管理器"""
    print("=== URL JSON MCP 2.0 异步管理器测试 ===")
    print(f"配置: 缓存时间={config.cache_duration_minutes}分钟, 超时={config.http_timeout}秒")
    
    # 测试URL
    test_urls = [
        "https://httpbin.org/json",
        "https://api.github.com/repos/microsoft/vscode",
        "https://httpbin.org/get"
    ]
    
    async with AsyncURLContentManager(cache_duration_minutes=5) as manager:
        for url in test_urls:
            print(f"\n--- 测试URL: {url} ---")
            
            try:
                # 测试JSON获取
                result = await manager.fetch_json(url)
                print(f"成功: {result['success']}")
                print(f"来源: {result.get('source', 'unknown')}")
                
                if result['success']:
                    data_size = len(str(result.get('data', {})))
                    print(f"数据大小: {data_size} 字符")
                    print(f"HTTP方法: {result.get('method', 'N/A')}")
                    print(f"状态码: {result.get('status_code', 'N/A')}")
                else:
                    print(f"错误类型: {result.get('error_type', 'unknown')}")
                    print(f"错误信息: {result.get('error', 'No error message')}")
                
                # 测试缓存
                print("\n--- 测试缓存 ---")
                cached_result = await manager.fetch_json(url)
                print(f"缓存命中: {cached_result.get('source') == 'cache'}")
                
            except Exception as e:
                print(f"异常: {str(e)}")
        
        # 测试编码规范获取
        print(f"\n--- 测试编码规范获取 ---")
        standards_url = "https://httpbin.org/get"
        domain = "Python, FastMCP, Async"
        
        try:
            result = await manager.fetch_coding_standards(standards_url, domain)
            print(f"成功: {result['success']}")
            print(f"Domain: {result.get('domain', 'N/A')}")
            print(f"来源: {result.get('source', 'unknown')}")
            
            if result['success']:
                content_size = len(result.get('content', ''))
                print(f"内容大小: {content_size} 字符")
        except Exception as e:
            print(f"异常: {str(e)}")
        
        # 测试缓存信息
        print(f"\n--- 缓存信息 ---")
        cache_info = manager.get_cache_info()
        print(f"缓存条目数: {cache_info['total_entries']}")
        print(f"缓存持续时间: {cache_info['cache_duration_minutes']} 分钟")
        
        # 测试缓存清理
        print(f"\n--- 测试缓存清理 ---")
        cleared_count = manager.clear_cache()
        print(f"清理了 {cleared_count} 个缓存条目")


async def test_error_handling():
    """测试错误处理"""
    print(f"\n=== 错误处理测试 ===")
    
    async with AsyncURLContentManager(cache_duration_minutes=1) as manager:
        # 测试无效URL
        invalid_urls = [
            "https://invalid-domain-that-does-not-exist.com/api",
            "https://httpbin.org/status/404",
            "https://httpbin.org/status/500"
        ]
        
        for url in invalid_urls:
            print(f"\n--- 测试错误URL: {url} ---")
            try:
                result = await manager.fetch_json(url)
                print(f"成功: {result['success']}")
                if not result['success']:
                    print(f"错误类型: {result.get('error_type', 'unknown')}")
                    print(f"错误信息: {result.get('error', 'No error message')}")
            except Exception as e:
                print(f"异常: {str(e)}")


async def main():
    """主测试函数"""
    try:
        await test_async_manager()
        await test_error_handling()
        print(f"\n=== 测试完成 ===")
    except KeyboardInterrupt:
        print(f"\n测试被用户中断")
    except Exception as e:
        print(f"\n测试异常: {str(e)}")


if __name__ == "__main__":
    # 设置测试环境
    os.environ['FASTMCP_LOG_LEVEL'] = 'INFO'
    os.environ['FASTMCP_CACHE_DURATION'] = '5'
    
    asyncio.run(main())
